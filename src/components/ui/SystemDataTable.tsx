"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Search, FileSpreadsheet, Trash2, Plus, Brain, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemData, SystemsService } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";

interface SystemDataTableProps {
  data: SystemData[];
  isLoading: boolean;
  isRTL: boolean;
  systemId: string;
  onImportClick: () => void;
  onDeleteAll?: () => void;
  onDataUpdate?: () => void;
}

export function SystemDataTable({ data, isLoading, isRTL, systemId, onImportClick, onDeleteAll, onDataUpdate }: SystemDataTableProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [isClassifying, setIsClassifying] = useState(false);
  const [classificationProgress, setClassificationProgress] = useState<{
    stage: 'idle' | 'table_classification' | 'page_classification' | 'completed';
    currentPage?: number;
    totalPages?: number;
  }>({ stage: 'idle' });
  const rowsPerPage = 20;

  // Filter data based on search term
  const filteredData = data.filter(row => 
    row.tableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    row.columnName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    row.dataType.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (row.schemaName && row.schemaName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Pagination
  const totalPages = Math.ceil(filteredData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  // AI Classification function
  const handleAIClassification = async () => {
    if (data.length === 0) {
      toast({
        title: isRTL ? "لا توجد بيانات" : "No Data",
        description: isRTL ? "لا توجد بيانات للتصنيف" : "No data available for classification",
        variant: "destructive"
      });
      return;
    }

    setIsClassifying(true);
    setClassificationProgress({ stage: 'table_classification' });

    try {
      // Step 1: Table Classification (Batch Processing)
      toast({
        title: isRTL ? "بدء التصنيف" : "Starting Classification",
        description: isRTL ? "تصنيف الجداول..." : "Classifying tables...",
      });

      const tableClassificationResponse = await fetch('/api/ai/classification/table-classification', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          systemData: data,
          systemId
        })
      });

      if (!tableClassificationResponse.ok) {
        const errorData = await tableClassificationResponse.json();
        throw new Error(errorData.error || 'Table classification failed');
      }

      const tableClassificationResult = await tableClassificationResponse.json();

      // Update data with table classifications
      const tableUpdates: Array<{
        documentId: string;
        data: Partial<Pick<SystemData, 'tableType' | 'dataCategory' | 'classificationStatus'>>;
      }> = [];

      data.forEach(record => {
        if (record.id) {
          const tableClassification = tableClassificationResult.classifications.find(
            (c: any) => c.tableName === record.tableName &&
                      (c.schemaName || 'default') === (record.schemaName || 'default')
          );

          if (tableClassification) {
            tableUpdates.push({
              documentId: record.id,
              data: {
                tableType: tableClassification.tableType,
                dataCategory: tableClassification.dataCategory,
                classificationStatus: 'table_classified'
              }
            });
          }
        }
      });

      // Batch update table classifications
      if (tableUpdates.length > 0) {
        await SystemsService.updateSystemDataBatch(systemId, tableUpdates);
      }

      // Step 2: Page-by-Page Classification
      setClassificationProgress({ stage: 'page_classification', currentPage: 1, totalPages });

      const pageUpdates: Array<{
        documentId: string;
        data: Partial<Pick<SystemData, 'confidentialityLevel' | 'hasPersonalData' | 'personalDataReason' | 'classificationStatus'>>;
      }> = [];

      for (let page = 1; page <= totalPages; page++) {
        setClassificationProgress({ stage: 'page_classification', currentPage: page, totalPages });

        const pageStartIndex = (page - 1) * rowsPerPage;
        const pageEndIndex = pageStartIndex + rowsPerPage;
        const pageData = filteredData.slice(pageStartIndex, pageEndIndex);

        // Get updated data with table classifications for this page
        const pageDataWithTableClassifications = pageData.map(record => {
          const tableUpdate = tableUpdates.find(u => u.documentId === record.id);
          return {
            ...record,
            tableType: tableUpdate?.data.tableType || record.tableType,
            dataCategory: tableUpdate?.data.dataCategory || record.dataCategory
          };
        });

        const pageClassificationResponse = await fetch('/api/ai/classification/page-classification', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            pageData: pageDataWithTableClassifications,
            systemId,
            pageNumber: page
          })
        });

        if (!pageClassificationResponse.ok) {
          const errorData = await pageClassificationResponse.json();
          throw new Error(errorData.error || `Page ${page} classification failed`);
        }

        const pageClassificationResult = await pageClassificationResponse.json();

        // Collect page classification updates
        pageClassificationResult.classifications.forEach((classification: any) => {
          pageUpdates.push({
            documentId: classification.recordId,
            data: {
              confidentialityLevel: classification.confidentialityLevel,
              hasPersonalData: classification.hasPersonalData,
              personalDataReason: classification.personalDataReason || undefined,
              classificationStatus: 'fully_classified'
            }
          });
        });
      }

      // Batch update page classifications
      if (pageUpdates.length > 0) {
        await SystemsService.updateSystemDataBatch(systemId, pageUpdates);
      }

      setClassificationProgress({ stage: 'completed' });

      toast({
        title: isRTL ? "تم التصنيف بنجاح" : "Classification Completed",
        description: isRTL ?
          `تم تصنيف ${data.length} سجل بنجاح` :
          `Successfully classified ${data.length} records`,
      });

      // Refresh data
      if (onDataUpdate) {
        onDataUpdate();
      }

    } catch (error) {
      console.error('Classification error:', error);
      toast({
        title: isRTL ? "خطأ في التصنيف" : "Classification Error",
        description: error instanceof Error ? error.message : "An error occurred during classification",
        variant: "destructive"
      });
    } finally {
      setIsClassifying(false);
      setClassificationProgress({ stage: 'idle' });
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div className="p-8 text-center">
          <div className="animate-pulse space-y-4">
            <div className="w-32 h-32 bg-gray-200 rounded-3xl mx-auto"></div>
            <div className="h-6 bg-gray-200 rounded w-48 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-32 mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden"
      >
        <div className="p-12 text-center">
          <div className="w-32 h-32 bg-[var(--brand-blue)]/10 rounded-3xl flex items-center justify-center mx-auto mb-8">
            <FileSpreadsheet className="w-16 h-16 text-[var(--brand-blue)]" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {isRTL ? "لا توجد بيانات" : "No Data Available"}
          </h3>
          <p className="text-gray-600 mb-8 max-w-md mx-auto">
            {isRTL 
              ? "لم يتم استيراد أي بيانات بعد. قم بتحميل ملف Excel لبدء إدارة بيانات النظام."
              : "No data has been imported yet. Upload an Excel file to start managing system data."
            }
          </p>
          <Button
            onClick={onImportClick}
            className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            {isRTL ? "استيراد البيانات" : "Import Data"}
          </Button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
              <FileSpreadsheet className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-white">
                {isRTL ? "بيانات النظام" : "System Data"}
              </h3>
              <p className="text-white/80">
                {isRTL ? `${data.length} سجل` : `${data.length} records`}
              </p>
            </div>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={handleAIClassification}
              disabled={isClassifying || data.length === 0}
              className="bg-purple-500/20 backdrop-blur-sm border border-purple-300/50 text-white hover:bg-purple-500/30 disabled:opacity-50"
            >
              {isClassifying ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Brain className="w-4 h-4 mr-2" />
              )}
              {isClassifying
                ? (isRTL ? "جاري التصنيف..." : "Classifying...")
                : (isRTL ? "تصنيف ذكي" : "AI Classify")
              }
            </Button>
            <Button
              onClick={onImportClick}
              className="bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:bg-white/30"
            >
              <Plus className="w-4 h-4 mr-2" />
              {isRTL ? "استيراد" : "Import"}
            </Button>
            {onDeleteAll && data.length > 0 && (
              <Button
                onClick={onDeleteAll}
                variant="outline"
                className="bg-red-500/20 border-red-300/50 text-white hover:bg-red-500/30"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                {isRTL ? "حذف الكل" : "Delete All"}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Search and Actions */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={isRTL ? "البحث في البيانات..." : "Search data..."}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1);
              }}
            />
          </div>
          <div className="text-sm text-gray-600">
            {isRTL
              ? `عرض ${startIndex + 1}-${Math.min(endIndex, filteredData.length)} من ${filteredData.length}`
              : `Showing ${startIndex + 1}-${Math.min(endIndex, filteredData.length)} of ${filteredData.length}`
            }
          </div>
        </div>

        {/* Classification Progress */}
        {classificationProgress.stage !== 'idle' && (
          <div className="mt-4 p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <div className="flex items-center gap-3">
              <Loader2 className="w-5 h-5 text-purple-600 animate-spin" />
              <div className="flex-1">
                <div className="text-sm font-medium text-purple-900">
                  {classificationProgress.stage === 'table_classification' && (
                    isRTL ? "تصنيف الجداول..." : "Classifying tables..."
                  )}
                  {classificationProgress.stage === 'page_classification' && (
                    isRTL
                      ? `تصنيف الصفحة ${classificationProgress.currentPage} من ${classificationProgress.totalPages}`
                      : `Classifying page ${classificationProgress.currentPage} of ${classificationProgress.totalPages}`
                  )}
                  {classificationProgress.stage === 'completed' && (
                    isRTL ? "تم التصنيف بنجاح" : "Classification completed"
                  )}
                </div>
                {classificationProgress.stage === 'page_classification' && classificationProgress.currentPage && classificationProgress.totalPages && (
                  <div className="mt-2 w-full bg-purple-200 rounded-full h-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(classificationProgress.currentPage / classificationProgress.totalPages) * 100}%` }}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "اسم المخطط" : "Schema Name"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "اسم الجدول" : "Table Name"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "اسم العمود" : "Column Name"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "نوع البيانات" : "Data Type"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "الحد الأقصى" : "Max Length"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "يقبل القيم الفارغة" : "Nullable"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "نوع الجدول" : "Table Type"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "فئة البيانات" : "Data Category"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "مستوى السرية" : "Confidentiality"}
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                {isRTL ? "بيانات شخصية" : "Personal Data"}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {currentData.map((row, index) => (
              <motion.tr
                key={`${row.id}-${index}`}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="hover:bg-gray-50 transition-colors"
              >
                <td className="px-6 py-4 text-sm text-gray-600">
                  {row.schemaName || '-'}
                </td>
                <td className="px-6 py-4 text-sm font-medium text-gray-900">
                  {row.tableName}
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {row.columnName}
                </td>
                <td className="px-6 py-4 text-sm">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[var(--brand-blue)]/10 text-[var(--brand-blue)]">
                    {row.dataType}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-600">
                  {row.maxLength || '-'}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.isNullable !== null ? (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      row.isNullable
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {row.isNullable ? (isRTL ? "نعم" : "Yes") : (isRTL ? "لا" : "No")}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.tableType ? (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      row.tableType === 'system_table'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {row.tableType === 'system_table'
                        ? (isRTL ? "جدول نظام" : "System")
                        : (isRTL ? "جدول بيانات" : "Data")
                      }
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.dataCategory ? (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      row.dataCategory === 'customers'
                        ? 'bg-purple-100 text-purple-800'
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {row.dataCategory === 'customers'
                        ? (isRTL ? "عملاء" : "Customers")
                        : (isRTL ? "فريق التطوير" : "Dev Team")
                      }
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.confidentialityLevel ? (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      row.confidentialityLevel === 'Public' ? 'bg-green-100 text-green-800' :
                      row.confidentialityLevel === 'Confidential' ? 'bg-yellow-100 text-yellow-800' :
                      row.confidentialityLevel === 'Secret' ? 'bg-orange-100 text-orange-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {isRTL ? (
                        row.confidentialityLevel === 'Public' ? 'عام' :
                        row.confidentialityLevel === 'Confidential' ? 'سري' :
                        row.confidentialityLevel === 'Secret' ? 'سري جداً' : 'سري للغاية'
                      ) : row.confidentialityLevel}
                    </span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className="px-6 py-4 text-sm">
                  {row.hasPersonalData !== undefined ? (
                    <div className="space-y-1">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        row.hasPersonalData
                          ? 'bg-red-100 text-red-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {row.hasPersonalData ? (isRTL ? "نعم" : "Yes") : (isRTL ? "لا" : "No")}
                      </span>
                      {row.hasPersonalData && row.personalDataReason && (
                        <div className="text-xs text-gray-500 max-w-xs truncate" title={row.personalDataReason}>
                          {row.personalDataReason}
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {isRTL 
                ? `صفحة ${currentPage} من ${totalPages}`
                : `Page ${currentPage} of ${totalPages}`
              }
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                variant="outline"
                size="sm"
              >
                {isRTL ? "السابق" : "Previous"}
              </Button>
              <Button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                variant="outline"
                size="sm"
              >
                {isRTL ? "التالي" : "Next"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
} 