import React from "react";
import { type Locale } from '@/i18n-config';
import ThiqahLayoutClient from "./ThiqahLayoutClient";

interface ThiqahLayoutProps {
  children: React.ReactNode;
  params: { lang: Locale };
}

export default function ThiqahLayout({ children, params }: ThiqahLayoutProps) {
  // Access params directly in server component
  const lang = params.lang;
  
  // Return the client component with the props it needs
  return (
    <div style={{ backgroundColor: 'var(--brand-blue)', minHeight: '100vh' }}>
      <ThiqahLayoutClient lang={lang}>
        {children}
      </ThiqahLayoutClient>
    </div>
  );
}
