"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import { ArrowLeft, ArrowRight, Shield, User, Mail, Database, Building, Calendar, FileText, BarChart, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemsService, System, SystemData, SystemContext } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import { ExcelImportModal } from "@/components/ui/ExcelImportModal";
import { SystemDataTable } from "@/components/ui/SystemDataTable";
import { ContextModal } from "@/components/ui/ContextModal";

interface SystemDetailsPageProps {
  params: { lang: Locale; systemId: string };
}

export default function SystemDetailsPage({ params }: SystemDetailsPageProps) {
  const { lang, systemId } = params;
  const isRTL = lang === "ar";
  const router = useRouter();
  const { toast } = useToast();

  const [system, setSystem] = useState<System | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [systemData, setSystemData] = useState<SystemData[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [isContextModalOpen, setIsContextModalOpen] = useState(false);
  const [systemContext, setSystemContext] = useState<SystemContext | null>(null);
  const [activeTab, setActiveTab] = useState<'details' | 'data' | 'classification'>('details');

  useEffect(() => {
    loadSystemDetails();
    loadSystemData();
    loadSystemContext();
  }, [systemId]);

  const loadSystemDetails = async () => {
    try {
      setIsLoading(true);
      const systems = await SystemsService.getSystems();
      const foundSystem = systems.find(s => s.id === systemId);
      
      if (foundSystem) {
        setSystem(foundSystem);
      } else {
        toast({
          title: isRTL ? "النظام غير موجود" : "System not found",
          description: isRTL ? "لم يتم العثور على النظام المطلوب" : "The requested system was not found",
          variant: "destructive",
        });
        router.push(`/${lang}/Thiqah/DataClassification`);
      }
    } catch (error) {
      toast({
        title: isRTL ? "خطأ في تحميل النظام" : "Error loading system",
        description: isRTL ? "فشل في تحميل تفاصيل النظام" : "Failed to load system details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadSystemData = async () => {
    try {
      setIsLoadingData(true);
      const data = await SystemsService.getSystemData(systemId);
      setSystemData(data);
    } catch (error) {
      console.error('Error loading system data:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  const loadSystemContext = async () => {
    try {
      const context = await SystemsService.getSystemContext(systemId);
      setSystemContext(context);
    } catch (error) {
      console.error('Error loading system context:', error);
    }
  };

  const handleImportData = async (data: Omit<SystemData, 'id' | 'createdAt' | 'systemId'>[]) => {
    try {
      await SystemsService.addSystemDataBatch(systemId, data);
      await loadSystemData(); // Refresh the data
      toast({
        title: isRTL ? "تم الاستيراد بنجاح" : "Import Successful",
        description: isRTL ? `تم استيراد ${data.length} سجل` : `Successfully imported ${data.length} records`,
      });
    } catch (error) {
      throw error; // Let the modal handle the error
    }
  };

  const handleDeleteAllData = async () => {
    if (!confirm(isRTL ? "هل أنت متأكد من حذف جميع البيانات؟" : "Are you sure you want to delete all data?")) {
      return;
    }

    try {
      await SystemsService.deleteAllSystemData(systemId);
      await loadSystemData(); // Refresh the data
      toast({
        title: isRTL ? "تم الحذف بنجاح" : "Delete Successful",
        description: isRTL ? "تم حذف جميع البيانات" : "All data has been deleted",
      });
    } catch (error) {
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete Error",
        description: isRTL ? "فشل في حذف البيانات" : "Failed to delete data",
        variant: "destructive",
      });
    }
  };

  const handleSaveContext = async (context: string) => {
    try {
      await SystemsService.saveSystemContext(systemId, context);
      await loadSystemContext(); // Refresh the context
    } catch (error) {
      throw error; // Let the modal handle the error
    }
  };

  const handleGoBack = () => {
    router.push(`/${lang}/Thiqah/DataClassification`);
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return "";
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString(lang === "ar" ? "ar-SA" : "en-US", {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

     const tabs = [
     {
       id: 'details' as const,
       label: isRTL ? "تفاصيل النظام" : "System Details",
       subtitle: isRTL ? "المعلومات الأساسية" : "Basic Information",
       icon: FileText
     },
     {
       id: 'data' as const,
       label: isRTL ? "بيانات النظام" : "System Data",
       subtitle: isRTL ? "إدارة البيانات" : "Data Management",
       icon: Database
     },
     {
       id: 'classification' as const,
       label: isRTL ? "التصنيف" : "Classification",
       subtitle: isRTL ? "مستويات الأمان" : "Security Levels",
       icon: Shield
     }
   ];

  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
        <div className="relative min-h-screen bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
          <div className="relative z-10 flex flex-col justify-center items-center min-h-screen px-8 py-16">
            <div className="animate-pulse text-center">
              <div className="w-24 h-24 bg-white/20 rounded-3xl mx-auto mb-8"></div>
              <div className="h-12 bg-white/20 rounded-lg w-96 mx-auto mb-4"></div>
              <div className="h-6 bg-white/20 rounded-lg w-64 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!system) {
    return null;
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Enhanced Hero Section with Glassmorphism Tabs */}
      <div className="relative min-h-screen bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
                 {/* Background Pattern */}
         <div className="absolute inset-0 opacity-10">
           <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
           <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
           <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-white rounded-full opacity-50"></div>
         </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, 20, -20],
                opacity: [0.3, 0.8, 0.3],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Back Button */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="absolute top-8 left-8 z-30"
        >
          <Button
            onClick={handleGoBack}
            className="bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 hover:text-white shadow-2xl"
          >
            {isRTL ? <ArrowRight className="w-4 h-4 mr-2" /> : <ArrowLeft className="w-4 h-4 mr-2" />}
            {isRTL ? "العودة" : "Back"}
          </Button>
        </motion.div>

                 {/* Context Button */}
         <motion.div
           initial={{ opacity: 0, x: 20 }}
           animate={{ opacity: 1, x: 0 }}
           transition={{ duration: 0.6 }}
           className="absolute top-8 right-8 z-30"
         >
           <Button
             onClick={() => setIsContextModalOpen(true)}
             className="bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 hover:text-white shadow-2xl"
           >
             <MessageSquare className="w-4 h-4 mr-2" />
             {isRTL ? "السياق" : "Context"}
             {systemContext && (
               <div className="w-2 h-2 bg-green-400 rounded-full ml-2 animate-pulse"></div>
             )}
           </Button>
         </motion.div>

        <div className="relative z-20 flex flex-col min-h-screen px-8 py-16">
          {/* System Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16 pt-16"
          >
            <div className="flex items-center justify-center gap-6 mb-8">
              <motion.div 
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="w-24 h-24 bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-xl rounded-3xl flex items-center justify-center shadow-2xl border border-white/20"
              >
                <Building className="w-12 h-12 text-white" />
              </motion.div>
              <div className="text-left">
                <motion.h1 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="text-5xl md:text-7xl font-bold text-white mb-2 tracking-tight"
                >
                  {system.name}
                </motion.h1>
                <motion.p 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="text-xl md:text-2xl text-white/90 font-medium"
                >
                  {isRTL ? "إدارة النظام المتقدمة" : "Advanced System Management"}
                </motion.p>
              </div>
            </div>

            {/* Enhanced System Info Cards */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto mb-16"
            >
              {[
                { icon: User, label: isRTL ? "المسؤول" : "Owner", value: system.responsibleOwner },
                { icon: Database, label: isRTL ? "مدير قاعدة البيانات" : "DBA", value: system.dba },
                { icon: Mail, label: isRTL ? "البريد الإلكتروني" : "Email", value: system.email }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1 + index * 0.1 }}
                  className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300 group"
                >
                  <div className="text-center">
                    <div className="flex justify-center mb-4">
                      <div className="p-3 rounded-xl bg-white/20 text-white group-hover:scale-110 transition-transform duration-300">
                        <item.icon className="w-6 h-6" />
                      </div>
                    </div>
                    <h3 className="text-sm font-semibold text-white/80 mb-2">{item.label}</h3>
                    <p className="text-lg font-bold text-white truncate" title={item.value}>{item.value}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Glassmorphism Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
            className="flex-1 flex flex-col"
          >
            {/* Tab Navigation */}
            <div className="flex justify-center mb-8">
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-2 border border-white/20 shadow-2xl">
                <div className="flex gap-2">
                  {tabs.map((tab) => (
                    <motion.button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`relative flex items-center gap-3 px-8 py-4 rounded-2xl transition-all duration-300 ${
                        activeTab === tab.id
                          ? 'bg-white text-gray-900 shadow-xl'
                          : 'text-white/80 hover:text-white hover:bg-white/10'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                                             <div className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 ${
                         activeTab === tab.id
                           ? 'bg-[var(--brand-blue)] text-white shadow-lg'
                           : 'bg-white/20 text-white/80'
                       }`}>
                        <tab.icon className="w-5 h-5" />
                      </div>
                      <div className="text-left">
                        <div className="font-bold text-sm">{tab.label}</div>
                        <div className={`text-xs ${activeTab === tab.id ? 'text-gray-600' : 'text-white/60'}`}>
                          {tab.subtitle}
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </div>
            </div>

            {/* Tab Content Container */}
            <div className="flex-1 bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl overflow-hidden">
              <div className="h-full overflow-y-auto">
                                 {/* System Details Tab */}
                 {activeTab === 'details' && (
                   <motion.div
                     key="details"
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ duration: 0.5 }}
                     className="p-8"
                   >
                     <div className="text-center mb-12">
                       <div className="flex justify-center mb-4">
                         <div className="w-16 h-16 bg-[var(--brand-blue)]/10 rounded-2xl flex items-center justify-center">
                           <FileText className="w-8 h-8 text-[var(--brand-blue)]" />
                         </div>
                       </div>
                       <h3 className="text-3xl font-bold text-white mb-2">
                         {isRTL ? "تفاصيل النظام الكاملة" : "Complete System Details"}
                       </h3>
                       <p className="text-white/80 text-lg">
                         {isRTL ? "جميع المعلومات المتعلقة بالنظام" : "All information related to the system"}
                       </p>
                     </div>

                     <div className="max-w-6xl mx-auto">
                       <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
                         {/* System Information */}
                         <div className="space-y-8">
                           <div>
                             <label className="text-sm font-semibold text-white/60 uppercase tracking-wider">
                               {isRTL ? "اسم النظام" : "System Name"}
                             </label>
                             <p className="text-2xl font-bold text-white mt-2">{system.name}</p>
                           </div>
                           
                           <div>
                             <label className="text-sm font-semibold text-white/60 uppercase tracking-wider">
                               {isRTL ? "المسؤول من ثقة" : "Responsible Owner from Thiqah"}
                             </label>
                             <p className="text-xl font-semibold text-white/90 mt-2">{system.responsibleOwner}</p>
                           </div>

                           <div>
                             <label className="text-sm font-semibold text-white/60 uppercase tracking-wider">
                               {isRTL ? "مدير قاعدة البيانات" : "Database Administrator"}
                             </label>
                             <p className="text-xl font-semibold text-white/90 mt-2">{system.dba}</p>
                           </div>
                         </div>

                         {/* Contact & Dates */}
                         <div className="space-y-8">
                           <div>
                             <label className="text-sm font-semibold text-white/60 uppercase tracking-wider">
                               {isRTL ? "البريد الإلكتروني" : "Email Address"}
                             </label>
                             <p className="text-xl font-semibold text-white/90 mt-2 break-all">{system.email}</p>
                           </div>

                           <div>
                             <label className="text-sm font-semibold text-white/60 uppercase tracking-wider">
                               {isRTL ? "تاريخ الإنشاء" : "Created Date"}
                             </label>
                             <p className="text-xl font-semibold text-white/90 mt-2">{formatDate(system.createdAt)}</p>
                           </div>

                           {system.updatedAt && (
                             <div>
                               <label className="text-sm font-semibold text-white/60 uppercase tracking-wider">
                                 {isRTL ? "آخر تحديث" : "Last Updated"}
                               </label>
                               <p className="text-xl font-semibold text-white/90 mt-2">{formatDate(system.updatedAt)}</p>
                             </div>
                           )}
                         </div>
                       </div>
                     </div>
                   </motion.div>
                 )}

                {/* System Data Tab */}
                {activeTab === 'data' && (
                  <motion.div
                    key="data"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="p-8"
                  >
                    <SystemDataTable
                      data={systemData}
                      isLoading={isLoadingData}
                      isRTL={isRTL}
                      onImportClick={() => setIsImportModalOpen(true)}
                      onDeleteAll={systemData.length > 0 ? handleDeleteAllData : undefined}
                    />
                  </motion.div>
                )}

                                 {/* Classification Tab */}
                 {activeTab === 'classification' && (
                   <motion.div
                     key="classification"
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ duration: 0.5 }}
                     className="p-8"
                   >
                     <div className="text-center py-20">
                       <div className="w-32 h-32 bg-[var(--brand-blue)]/10 rounded-3xl flex items-center justify-center mx-auto mb-8">
                         <Shield className="w-16 h-16 text-[var(--brand-blue)]" />
                       </div>
                       <h3 className="text-4xl font-bold text-white mb-4">
                         {isRTL ? "تصنيف البيانات" : "Data Classification"}
                       </h3>
                       <p className="text-xl text-white/80 max-w-2xl mx-auto">
                         {isRTL 
                           ? "سيتم عرض مستويات التصنيف والأمان للبيانات هنا"
                           : "Data classification levels and security measures will be displayed here"
                         }
                       </p>
                     </div>
                   </motion.div>
                 )}
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Modals */}
      <ExcelImportModal
        isOpen={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        onImport={handleImportData}
        isRTL={isRTL}
      />

      <ContextModal
        isOpen={isContextModalOpen}
        onClose={() => setIsContextModalOpen(false)}
        onSave={handleSaveContext}
        isRTL={isRTL}
        currentContext={systemContext?.context || ""}
      />
    </div>
  );
} 