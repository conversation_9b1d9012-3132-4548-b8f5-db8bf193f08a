import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { Ratelimit } from '@upstash/ratelimit';
import { kv } from '@vercel/kv';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 60 seconds for page analysis
export const maxDuration = 60;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

let ratelimit: Ratelimit | null = null;

// Initialize rate limiter only if KV is configured
// TODO: Replace with actual rate limit confirmed by user
if (process.env.KV_URL) {
  ratelimit = new Ratelimit({
    redis: kv,
    limiter: Ratelimit.fixedWindow(500, '30d'), // Placeholder: 500 page classifications per month
    analytics: true,
    prefix: 'ai_page_classification',
  });
} else {
  console.warn("Rate limiting disabled: Vercel KV environment variables not found.");
}

// Helper function to get IP address from request headers
function getIpAddress(req: NextRequest): string {
  let ip = req.headers.get('x-forwarded-for')?.split(',')[0].trim();
  if (ip) return ip;
  ip = req.headers.get('x-real-ip')?.trim();
  if (ip) return ip;
  return '127.0.0.1'; // Default fallback
}

// Schema for page classification response
const PageClassificationSchema = z.object({
  records: z.array(z.object({
    recordId: z.string(),
    confidentialityLevel: z.enum(["Public", "Confidential", "Secret", "Top Secret"]),
    hasPersonalData: z.boolean(),
    personalDataReason: z.string().optional(),
    reasoning: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  pageData: z.array(z.object({
    id: z.string(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    maxLength: z.number().nullable().optional(),
    isNullable: z.boolean().nullable().optional(),
    tableType: z.enum(["system_table", "data_table"]).optional(),
    dataCategory: z.enum(["customers", "development_team"]).optional()
  })),
  systemId: z.string(),
  pageNumber: z.number()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const { pageData, systemId, pageNumber } = RequestSchema.parse(body);

    if (!pageData || pageData.length === 0) {
      return NextResponse.json(
        { error: 'No page data provided for classification' },
        { status: 400 }
      );
    }

    // Rate limiting check
    if (ratelimit) {
      const ip = getIpAddress(req);
      const { success, remaining } = await ratelimit.limit(ip);
      
      if (!success) {
        return NextResponse.json(
          { 
            error: 'Page classification rate limit exceeded',
            feature: 'page_classification',
            remaining: 0
          },
          { status: 429 }
        );
      }
    }

    // Prepare prompt for AI classification
    const prompt = `
You are a data security expert tasked with classifying database records for confidentiality and personal data identification.

For each record, determine:
1. Confidentiality Level: "Public", "Confidential", "Secret", or "Top Secret"
2. Personal Data Status: true/false (whether the record contains personal data)
3. If personal data = true, provide exactly 30 words explaining why

Records to classify (Page ${pageNumber}):
${pageData.map(record => `
Record ID: ${record.id}
Table: ${record.schemaName ? `${record.schemaName}.` : ''}${record.tableName}
Column: ${record.columnName}
Data Type: ${record.dataType}${record.maxLength ? `(${record.maxLength})` : ''}
Nullable: ${record.isNullable ? 'Yes' : 'No'}
Table Type: ${record.tableType || 'Unknown'}
Data Category: ${record.dataCategory || 'Unknown'}
`).join('\n')}

Classification Guidelines:
- Public: Non-sensitive data that can be freely shared
- Confidential: Internal business data that should not be public
- Secret: Sensitive data requiring special protection
- Top Secret: Highly sensitive data with strict access controls

Personal Data Indicators:
- Names, emails, phone numbers, addresses
- Personal identifiers, social security numbers
- Biometric data, health information
- Financial account information
- Any data that can identify an individual

When personal data is detected, provide exactly 30 words explaining why this data is considered personal.
`;

    // Use standard model for classification tasks
    const result = await generateObject({
      model: google('gemini-2.0-flash-exp'),
      prompt: prompt,
      schema: PageClassificationSchema,
      maxTokens: 3000,
    });

    // Validate personal data reasons are exactly 30 words
    const processedRecords = result.object.records.map(record => {
      if (record.hasPersonalData && record.personalDataReason) {
        const wordCount = record.personalDataReason.trim().split(/\s+/).length;
        if (wordCount !== 30) {
          // Truncate or pad to exactly 30 words
          const words = record.personalDataReason.trim().split(/\s+/);
          if (words.length > 30) {
            record.personalDataReason = words.slice(0, 30).join(' ');
          } else {
            // Pad with generic words to reach 30
            while (words.length < 30) {
              words.push('data');
            }
            record.personalDataReason = words.join(' ');
          }
        }
      }
      return record;
    });

    // Return classification results
    return NextResponse.json({
      success: true,
      feature: 'page_classification',
      systemId,
      pageNumber,
      classifications: processedRecords,
      remaining: ratelimit ? (await ratelimit.limit(getIpAddress(req))).remaining : null,
      usage: result.usage
    });

  } catch (error) {
    console.error('Error in page classification:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during page classification' },
      { status: 500 }
    );
  }
}
