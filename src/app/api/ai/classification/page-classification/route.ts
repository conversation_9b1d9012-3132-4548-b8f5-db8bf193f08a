import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 60 seconds for page analysis
export const maxDuration = 60;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for page classification response
const PageClassificationSchema = z.object({
  records: z.array(z.object({
    recordId: z.string(),
    confidentialityLevel: z.enum(["Public", "Confidential", "Secret", "Top Secret"]),
    confidentialityReasoning: z.string(),
    hasPersonalData: z.boolean(),
    personalDataReason: z.string().optional(),
    personalDataReasoning: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  pageData: z.array(z.object({
    id: z.string(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    maxLength: z.number().nullable().optional(),
    isNullable: z.boolean().nullable().optional(),
    tableType: z.enum(["system_table", "data_table"]).optional(),
    dataCategory: z.enum(["customers", "development_team"]).optional()
  })),
  systemId: z.string(),
  pageNumber: z.number(),
  startIndex: z.number(),
  endIndex: z.number()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const { pageData, systemId, pageNumber, startIndex, endIndex } = RequestSchema.parse(body);

    if (!pageData || pageData.length === 0) {
      return NextResponse.json(
        { error: 'No page data provided for classification' },
        { status: 400 }
      );
    }



    // Prepare prompt for AI classification with SABER context
    const prompt = `You are a data classification expert for SABER (Saudi Standards, Metrology and Quality Organization). SABER is the Saudi regulatory platform for product conformity assessment and market surveillance.

SABER Context:
- SABER manages product conformity certificates, technical regulations, and market surveillance
- Handles sensitive business data including facility information, certificates, bills, and regulatory compliance
- Processes personal data of facility owners, legal representatives, and contact officers
- Manages confidential technical specifications, test results, and regulatory decisions
- Contains financial data including bills, payments, and guarantees

Classify these ${pageData.length} database records (${startIndex}-${endIndex}) in SABER context:

${pageData.map(record => `${record.id}: ${record.tableName}.${record.columnName} (${record.dataType})`).join('\n')}

For each record, provide:
1. confidentialityLevel: "Public", "Confidential", "Secret", "Top Secret"
2. confidentialityReasoning: Explain why this confidentiality level (consider SABER regulatory context)
3. hasPersonalData: true/false
4. personalDataReasoning: Explain personal data decision (consider GDPR/Saudi data protection)
5. personalDataReason: exactly 30 words if hasPersonalData=true, otherwise omit

SABER-Specific Guidelines:
- Public: Non-sensitive regulatory information, public standards
- Confidential: Internal business operations, facility details, non-sensitive certificates
- Secret: Sensitive regulatory decisions, detailed technical specifications, financial transactions
- Top Secret: Critical security data, sensitive personal information, confidential regulatory intelligence

Personal Data in SABER Context:
- Facility owner details, legal representative information
- Contact officer names, emails, phones
- User credentials, identity information
- Financial account details, payment information

Return JSON: {"records": [{"recordId": "...", "confidentialityLevel": "...", "confidentialityReasoning": "...", "hasPersonalData": true/false, "personalDataReasoning": "...", "personalDataReason": "..."}]}`;

    // Use gemini-2.5-pro for better classification with SABER context
    const result = await generateObject({
      model: google('gemini-2.0-flash-exp'),
      prompt: prompt,
      schema: PageClassificationSchema,
      maxTokens: 4000,
    });

    // Validate personal data reasons are exactly 30 words
    const processedRecords = result.object.records.map(record => {
      if (record.hasPersonalData && record.personalDataReason) {
        const wordCount = record.personalDataReason.trim().split(/\s+/).length;
        if (wordCount !== 30) {
          // Truncate or pad to exactly 30 words
          const words = record.personalDataReason.trim().split(/\s+/);
          if (words.length > 30) {
            record.personalDataReason = words.slice(0, 30).join(' ');
          } else {
            // Pad with generic words to reach 30
            while (words.length < 30) {
              words.push('data');
            }
            record.personalDataReason = words.join(' ');
          }
        }
      }
      return record;
    });

    // Return classification results
    return NextResponse.json({
      success: true,
      feature: 'page_classification',
      systemId,
      pageNumber,
      startIndex,
      endIndex,
      recordsProcessed: pageData.length,
      classifications: processedRecords,
      usage: result.usage
    });

  } catch (error) {
    console.error('Error in page classification:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during page classification' },
      { status: 500 }
    );
  }
}
