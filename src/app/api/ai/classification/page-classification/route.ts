import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 60 seconds for page analysis
export const maxDuration = 60;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for page classification response
const PageClassificationSchema = z.object({
  records: z.array(z.object({
    recordId: z.string(),
    confidentialityLevel: z.enum(["Public", "Confidential", "Secret", "Top Secret"]),
    confidentialityReasoning: z.string(),
    hasPersonalData: z.boolean(),
    personalDataReasoning: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  pageData: z.array(z.object({
    id: z.string(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    maxLength: z.number().nullable().optional(),
    isNullable: z.boolean().nullable().optional(),
    tableType: z.enum(["system_table", "data_table"]).optional(),
    dataCategory: z.enum(["customers", "development_team"]).optional()
  })),
  systemId: z.string(),
  pageNumber: z.number(),
  startIndex: z.number(),
  endIndex: z.number()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const { pageData, systemId, pageNumber, startIndex, endIndex } = RequestSchema.parse(body);

    if (!pageData || pageData.length === 0) {
      return NextResponse.json(
        { error: 'No page data provided for classification' },
        { status: 400 }
      );
    }



    // Prepare prompt for AI classification with SABER context
    const prompt = `Classify ${pageData.length} database fields with detailed reasoning:

${pageData.map(record => `${record.id}: ${record.tableName}.${record.columnName} (${record.dataType})`).join('\n')}

Provide detailed, specific reasoning that explains WHY each field has its classification:

For confidentialityLevel (Public/Confidential/Secret/Top Secret):
- Explain what type of data this field contains and why it needs protection
- Consider business impact if exposed
- Be specific about the data sensitivity

For personalDataReasoning:
- If true: Explain exactly what personal information this contains
- If false: Explain why this is not personal data
- Be specific about data protection implications

JSON format: {"records": [{"recordId": "...", "confidentialityLevel": "...", "confidentialityReasoning": "...", "hasPersonalData": true/false, "personalDataReasoning": "..."}]}`;

    let processedRecords;
    let usage = null;

    try {
      console.log(`Sending prompt to AI: ${prompt}`);

      // Use gemini-2.5-flash for better classification with SABER context
      const result = await generateObject({
        model: google('gemini-2.5-flash'),
        prompt: prompt,
        schema: PageClassificationSchema,
        maxTokens: 8000,
      });

      console.log('AI response received:', result.object);
      processedRecords = result.object.records;
      usage = result.usage;

    } catch (aiError) {
      console.error('AI classification failed, using fallback rules:', aiError);

      // Fallback: Use intelligent rule-based classification with detailed reasoning
      processedRecords = pageData.map(record => {
        const columnName = record.columnName.toLowerCase();
        const tableName = record.tableName.toLowerCase();
        const dataType = record.dataType?.toLowerCase() || '';

        // Detailed confidentiality classification
        let confidentialityLevel = "Confidential";
        let confidentialityReasoning = `Internal business data in ${record.tableName} table requires protection from unauthorized access`;

        if (columnName.includes('id') && !columnName.includes('userid') && !columnName.includes('ownerid')) {
          confidentialityLevel = "Public";
          confidentialityReasoning = `System identifier used for database relationships, no sensitive business information`;
        } else if (columnName.includes('password') || columnName.includes('secret') || columnName.includes('token') || columnName.includes('hash')) {
          confidentialityLevel = "Top Secret";
          confidentialityReasoning = `Authentication credentials that could compromise system security if exposed`;
        } else if (columnName.includes('xml') || columnName.includes('path') || columnName.includes('file') || columnName.includes('key')) {
          confidentialityLevel = "Secret";
          confidentialityReasoning = `System configuration or file paths that could reveal infrastructure details`;
        } else if (columnName.includes('amount') || columnName.includes('price') || columnName.includes('cost') || columnName.includes('bill')) {
          confidentialityLevel = "Confidential";
          confidentialityReasoning = `Financial data that could impact business operations if disclosed`;
        } else if (columnName.includes('createdon') || columnName.includes('updatedon') || columnName.includes('date')) {
          confidentialityLevel = "Public";
          confidentialityReasoning = `Timestamp data used for auditing and system operations`;
        }

        // Detailed personal data classification
        const hasPersonalData = columnName.includes('name') ||
                               columnName.includes('email') ||
                               columnName.includes('phone') ||
                               columnName.includes('mobile') ||
                               columnName.includes('username') ||
                               columnName.includes('owner') ||
                               columnName.includes('createdby') ||
                               columnName.includes('updatedby') ||
                               columnName.includes('user');

        let personalDataReasoning;
        if (hasPersonalData) {
          if (columnName.includes('email')) {
            personalDataReasoning = `Contains email addresses which are personal identifiers subject to data protection regulations`;
          } else if (columnName.includes('phone') || columnName.includes('mobile')) {
            personalDataReasoning = `Contains phone numbers which are personal contact information requiring privacy protection`;
          } else if (columnName.includes('name')) {
            personalDataReasoning = `Contains personal names which directly identify individuals and require data protection`;
          } else if (columnName.includes('username') || columnName.includes('user')) {
            personalDataReasoning = `Contains user identifiers that can be linked to specific individuals`;
          } else if (columnName.includes('createdby') || columnName.includes('updatedby') || columnName.includes('owner')) {
            personalDataReasoning = `Contains user references that can identify who performed actions in the system`;
          } else {
            personalDataReasoning = `Contains personal information that identifies or relates to specific individuals`;
          }
        } else {
          if (columnName.includes('id') && !columnName.includes('userid')) {
            personalDataReasoning = `System-generated identifier with no direct personal information`;
          } else if (columnName.includes('date') || columnName.includes('time')) {
            personalDataReasoning = `Timestamp data that doesn't identify specific individuals`;
          } else if (columnName.includes('status') || columnName.includes('type') || columnName.includes('flag')) {
            personalDataReasoning = `System status or configuration data not linked to individuals`;
          } else if (columnName.includes('amount') || columnName.includes('count') || columnName.includes('number')) {
            personalDataReasoning = `Numerical business data not containing personal identifiers`;
          } else {
            personalDataReasoning = `Business or system data that doesn't contain personal identifiers`;
          }
        }

        return {
          recordId: record.id,
          confidentialityLevel,
          confidentialityReasoning,
          hasPersonalData,
          personalDataReasoning
        };
      });

      // Set fallback usage info
      usage = {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
      };
    }

    // Return classification results
    return NextResponse.json({
      success: true,
      feature: 'page_classification',
      systemId,
      pageNumber,
      startIndex,
      endIndex,
      recordsProcessed: pageData.length,
      classifications: processedRecords,
      usage: usage
    });

  } catch (error) {
    console.error('Error in page classification:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during page classification' },
      { status: 500 }
    );
  }
}
