import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 60 seconds for page analysis
export const maxDuration = 60;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for page classification response
const PageClassificationSchema = z.object({
  records: z.array(z.object({
    recordId: z.string(),
    confidentialityLevel: z.enum(["Public", "Confidential", "Secret", "Top Secret"]),
    confidentialityReasoning: z.string(),
    hasPersonalData: z.boolean(),
    personalDataReasoning: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  pageData: z.array(z.object({
    id: z.string(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    maxLength: z.number().nullable().optional(),
    isNullable: z.boolean().nullable().optional(),
    tableType: z.enum(["system_table", "data_table"]).optional(),
    dataCategory: z.enum(["customers", "development_team"]).optional()
  })),
  systemId: z.string(),
  pageNumber: z.number(),
  startIndex: z.number(),
  endIndex: z.number()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const { pageData, systemId, pageNumber, startIndex, endIndex } = RequestSchema.parse(body);

    if (!pageData || pageData.length === 0) {
      return NextResponse.json(
        { error: 'No page data provided for classification' },
        { status: 400 }
      );
    }



    // Prepare prompt for AI classification with SABER context
    const prompt = `Classify ${pageData.length} fields. Use EXACTLY 1-2 words for reasoning:

${pageData.map(record => `${record.id}: ${record.tableName}.${record.columnName}`).join('\n')}

Rules:
- confidentialityLevel: Public/Confidential/Secret/Top Secret
- confidentialityReasoning: 1-2 words only (e.g. "Internal ID", "User data", "File path")
- hasPersonalData: true/false
- personalDataReasoning: 1-2 words only (e.g. "No PII", "Username field", "Email data")

JSON format: {"records": [{"recordId": "...", "confidentialityLevel": "...", "confidentialityReasoning": "...", "hasPersonalData": true/false, "personalDataReasoning": "..."}]}`;

    let processedRecords;
    let usage = null;

    try {
      console.log(`Sending prompt to AI: ${prompt}`);

      // Use gemini-2.5-flash for better classification with SABER context
      const result = await generateObject({
        model: google('gemini-2.5-flash'),
        prompt: prompt,
        schema: PageClassificationSchema,
        maxTokens: 8000,
      });

      console.log('AI response received:', result.object);
      processedRecords = result.object.records;
      usage = result.usage;

    } catch (aiError) {
      console.error('AI classification failed, using fallback rules:', aiError);

      // Fallback: Use simple rule-based classification
      processedRecords = pageData.map(record => {
        const columnName = record.columnName.toLowerCase();

        // Simple confidentiality rules
        let confidentialityLevel = "Confidential";
        let confidentialityReasoning = "Internal data";

        if (columnName.includes('id') || columnName.includes('key')) {
          confidentialityLevel = "Public";
          confidentialityReasoning = "ID field";
        } else if (columnName.includes('password') || columnName.includes('secret') || columnName.includes('token')) {
          confidentialityLevel = "Secret";
          confidentialityReasoning = "Security data";
        } else if (columnName.includes('xml') || columnName.includes('path') || columnName.includes('file')) {
          confidentialityLevel = "Secret";
          confidentialityReasoning = "File data";
        }

        // Simple personal data rules
        const hasPersonalData = columnName.includes('name') ||
                               columnName.includes('email') ||
                               columnName.includes('phone') ||
                               columnName.includes('mobile') ||
                               columnName.includes('user') ||
                               columnName.includes('owner') ||
                               columnName.includes('createdby') ||
                               columnName.includes('updatedby');

        const personalDataReasoning = hasPersonalData ? "User info" : "No PII";

        return {
          recordId: record.id,
          confidentialityLevel,
          confidentialityReasoning,
          hasPersonalData,
          personalDataReasoning
        };
      });

      // Set fallback usage info
      usage = {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
      };
    }

    // Return classification results
    return NextResponse.json({
      success: true,
      feature: 'page_classification',
      systemId,
      pageNumber,
      startIndex,
      endIndex,
      recordsProcessed: pageData.length,
      classifications: processedRecords,
      usage: usage
    });

  } catch (error) {
    console.error('Error in page classification:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during page classification' },
      { status: 500 }
    );
  }
}
