import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 60 seconds for page analysis
export const maxDuration = 60;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for page classification response
const PageClassificationSchema = z.object({
  records: z.array(z.object({
    recordId: z.string(),
    confidentialityLevel: z.enum(["Public", "Confidential", "Secret", "Top Secret"]),
    confidentialityReasoning: z.string(),
    hasPersonalData: z.boolean(),
    personalDataReason: z.string().optional(),
    personalDataReasoning: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  pageData: z.array(z.object({
    id: z.string(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    maxLength: z.number().nullable().optional(),
    isNullable: z.boolean().nullable().optional(),
    tableType: z.enum(["system_table", "data_table"]).optional(),
    dataCategory: z.enum(["customers", "development_team"]).optional()
  })),
  systemId: z.string(),
  pageNumber: z.number(),
  startIndex: z.number(),
  endIndex: z.number()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const { pageData, systemId, pageNumber, startIndex, endIndex } = RequestSchema.parse(body);

    if (!pageData || pageData.length === 0) {
      return NextResponse.json(
        { error: 'No page data provided for classification' },
        { status: 400 }
      );
    }



    // Prepare prompt for AI classification with SABER context
    const prompt = `SABER data classification expert. Classify ${pageData.length} records:

${pageData.map(record => `${record.id}: ${record.tableName}.${record.columnName}`).join('\n')}

SABER context: Saudi regulatory platform for product conformity, certificates, facility data, bills.

For each record:
- confidentialityLevel: Public/Confidential/Secret/Top Secret
- confidentialityReasoning: Brief reason (max 10 words)
- hasPersonalData: true/false
- personalDataReasoning: Brief reason (max 10 words)
- personalDataReason: exactly 30 words if hasPersonalData=true

Guidelines:
- Public: Non-sensitive regulatory info
- Confidential: Internal business data
- Secret: Sensitive regulatory/financial data
- Top Secret: Critical security/personal data

Personal data: names, emails, phones, IDs, financial info

JSON: {"records": [{"recordId": "...", "confidentialityLevel": "...", "confidentialityReasoning": "...", "hasPersonalData": true/false, "personalDataReasoning": "...", "personalDataReason": "..."}]}`;

    // Use gemini-2.5-flash for better classification with SABER context
    const result = await generateObject({
      model: google('gemini-2.5-flash'),
      prompt: prompt,
      schema: PageClassificationSchema,
      maxTokens: 6000,
    });

    // Validate personal data reasons are exactly 30 words
    const processedRecords = result.object.records.map(record => {
      if (record.hasPersonalData && record.personalDataReason) {
        const wordCount = record.personalDataReason.trim().split(/\s+/).length;
        if (wordCount !== 30) {
          // Truncate or pad to exactly 30 words
          const words = record.personalDataReason.trim().split(/\s+/);
          if (words.length > 30) {
            record.personalDataReason = words.slice(0, 30).join(' ');
          } else {
            // Pad with generic words to reach 30
            while (words.length < 30) {
              words.push('data');
            }
            record.personalDataReason = words.join(' ');
          }
        }
      }
      return record;
    });

    // Return classification results
    return NextResponse.json({
      success: true,
      feature: 'page_classification',
      systemId,
      pageNumber,
      startIndex,
      endIndex,
      recordsProcessed: pageData.length,
      classifications: processedRecords,
      usage: result.usage
    });

  } catch (error) {
    console.error('Error in page classification:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during page classification' },
      { status: 500 }
    );
  }
}
