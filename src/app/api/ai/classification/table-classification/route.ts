import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { Ratelimit } from '@upstash/ratelimit';
import { kv } from '@vercel/kv';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 120 seconds for complex table analysis
export const maxDuration = 120;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

let ratelimit: Ratelimit | null = null;

// Initialize rate limiter only if KV is configured
// TODO: Replace with actual rate limit confirmed by user
if (process.env.KV_URL) {
  ratelimit = new Ratelimit({
    redis: kv,
    limiter: Ratelimit.fixedWindow(100, '30d'), // Placeholder: 100 table classifications per month
    analytics: true,
    prefix: 'ai_table_classification',
  });
} else {
  console.warn("Rate limiting disabled: Vercel KV environment variables not found.");
}

// Helper function to get IP address from request headers
function getIpAddress(req: NextRequest): string {
  let ip = req.headers.get('x-forwarded-for')?.split(',')[0].trim();
  if (ip) return ip;
  ip = req.headers.get('x-real-ip')?.trim();
  if (ip) return ip;
  return '127.0.0.1'; // Default fallback
}

// Schema for table classification response
const TableClassificationSchema = z.object({
  tables: z.array(z.object({
    tableName: z.string(),
    schemaName: z.string().optional(),
    tableType: z.enum(["system_table", "data_table"]),
    dataCategory: z.enum(["customers", "development_team"]),
    reasoning: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  systemData: z.array(z.object({
    id: z.string().optional(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    maxLength: z.number().nullable().optional(),
    isNullable: z.boolean().nullable().optional()
  })),
  systemId: z.string()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const { systemData, systemId } = RequestSchema.parse(body);

    if (!systemData || systemData.length === 0) {
      return NextResponse.json(
        { error: 'No system data provided for classification' },
        { status: 400 }
      );
    }

    // Rate limiting check
    if (ratelimit) {
      const ip = getIpAddress(req);
      const { success, remaining } = await ratelimit.limit(ip);
      
      if (!success) {
        return NextResponse.json(
          { 
            error: 'Table classification rate limit exceeded',
            feature: 'table_classification',
            remaining: 0
          },
          { status: 429 }
        );
      }
    }

    // Extract unique tables from system data
    const uniqueTables = Array.from(
      new Map(
        systemData.map(item => [
          `${item.schemaName || 'default'}.${item.tableName}`,
          {
            tableName: item.tableName,
            schemaName: item.schemaName,
            columns: systemData
              .filter(d => d.tableName === item.tableName && d.schemaName === item.schemaName)
              .map(d => ({
                columnName: d.columnName,
                dataType: d.dataType,
                maxLength: d.maxLength,
                isNullable: d.isNullable
              }))
          }
        ])
      ).values()
    );

    // Prepare prompt for AI classification
    const prompt = `
You are a database expert tasked with classifying database tables. Analyze the following tables and their columns to determine:

1. Table Type: "system_table" (system/configuration tables) or "data_table" (business data tables)
2. Data Category: "customers" (customer-related data) or "development_team" (development/technical data)

Tables to classify:
${uniqueTables.map(table => `
Table: ${table.schemaName ? `${table.schemaName}.` : ''}${table.tableName}
Columns:
${table.columns.map(col => `  - ${col.columnName} (${col.dataType}${col.maxLength ? `(${col.maxLength})` : ''}, ${col.isNullable ? 'nullable' : 'not null'})`).join('\n')}
`).join('\n')}

Classification Guidelines:
- System tables: Configuration, metadata, logs, system settings, user management, permissions
- Data tables: Business data, customer information, transactions, products, orders
- Customer category: Tables containing customer data, user profiles, customer transactions
- Development team category: Technical tables, logs, system configurations, development artifacts

Provide reasoning for each classification decision.
`;

    // Use thinking model for complex table analysis
    const result = await generateObject({
      model: google('gemini-2.0-flash-thinking-exp-1219'),
      prompt: prompt,
      schema: TableClassificationSchema,
      maxTokens: 4000,
    });

    // Return classification results
    return NextResponse.json({
      success: true,
      feature: 'table_classification',
      systemId,
      classifications: result.object.tables,
      remaining: ratelimit ? (await ratelimit.limit(getIpAddress(req))).remaining : null,
      usage: result.usage
    });

  } catch (error) {
    console.error('Error in table classification:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error during table classification' },
      { status: 500 }
    );
  }
}
