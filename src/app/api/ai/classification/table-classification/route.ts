import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow processing up to 120 seconds for complex table analysis
export const maxDuration = 120;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for table classification response
const TableClassificationSchema = z.object({
  tables: z.array(z.object({
    tableName: z.string(),
    schemaName: z.string().optional(),
    tableType: z.enum(["system_table", "data_table"]),
    dataCategory: z.enum(["customers", "development_team"]),
    reasoning: z.string()
  }))
});

// Schema for request body
const RequestSchema = z.object({
  systemData: z.array(z.object({
    id: z.string().optional(),
    schemaName: z.string().optional(),
    tableName: z.string(),
    columnName: z.string(),
    dataType: z.string(),
    maxLength: z.number().nullable().optional(),
    isNullable: z.boolean().nullable().optional()
  })),
  systemId: z.string()
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    console.log('Received request body:', {
      systemDataLength: body.systemData?.length,
      systemId: body.systemId,
      sampleData: body.systemData?.slice(0, 2) // Log first 2 records for debugging
    });

    const { systemData, systemId } = RequestSchema.parse(body);

    if (!systemData || systemData.length === 0) {
      return NextResponse.json(
        { error: 'No system data provided for classification' },
        { status: 400 }
      );
    }



    // Extract unique tables from system data
    const uniqueTables = Array.from(
      new Map(
        systemData.map(item => [
          `${item.schemaName || 'default'}.${item.tableName}`,
          {
            tableName: item.tableName,
            schemaName: item.schemaName,
            columns: systemData
              .filter(d => d.tableName === item.tableName && d.schemaName === item.schemaName)
              .map(d => ({
                columnName: d.columnName,
                dataType: d.dataType,
                maxLength: d.maxLength,
                isNullable: d.isNullable
              }))
          }
        ])
      ).values()
    );

    // Prepare prompt for AI classification
    const prompt = `You are a database expert. Classify each table with:
1. tableType: "system_table" or "data_table"
2. dataCategory: "customers" or "development_team"

Tables:
${uniqueTables.map(table => `
Table: ${table.tableName}
Columns: ${table.columns.map(col => col.columnName).join(', ')}
`).join('\n')}

Rules:
- system_table: config, logs, metadata, settings
- data_table: business data, user content
- customers: user/customer related data
- development_team: technical/system data

Return JSON with tables array containing tableName, tableType, dataCategory, reasoning for each table.`;

    console.log('Sending prompt to AI:', prompt);
    console.log('Unique tables to classify:', uniqueTables.length);

    // Use model for table analysis
    const result = await generateObject({
      model: google('gemini-1.5-flash'),
      prompt: prompt,
      schema: TableClassificationSchema,
      maxTokens: 2000,
    });

    console.log('AI response received:', result.object);

    // Return classification results
    return NextResponse.json({
      success: true,
      feature: 'table_classification',
      systemId,
      classifications: result.object.tables,
      usage: result.usage
    });

  } catch (error) {
    console.error('Error in table classification:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined
    });

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Internal server error during table classification',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
